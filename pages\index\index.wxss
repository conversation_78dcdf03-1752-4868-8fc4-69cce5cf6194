/* pages/index/index.wxss */

.floating-btn {
  position: fixed;
  bottom: 100rpx;
  right: 40rpx;
  width: 100rpx;
  height: 100rpx;
  background-color: #1aad19;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 20rpx rgba(26, 173, 25, 0.4);
  z-index: 999;
}

.floating-btn text {
  color: white;
  font-size: 48rpx;
  font-weight: bold;
}

.floating-btn:active {
  transform: scale(0.95);
  transition: transform 0.1s;
}

/* 卡片悬停效果 */
.card:active {
  transform: scale(0.98);
  transition: transform 0.1s;
}

/* 统计信息样式 */
.statistics {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #1aad19;
}

.stat-label {
  font-size: 24rpx;
  color: #999;
  margin-top: 5rpx;
}

/* 搜索框样式 */
.search-container {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: #f5f5f5;
  padding-bottom: 10rpx;
}

/* 首页搜索框特殊样式 */
.page .card .form-input {
  padding: 35rpx 25rpx;
  font-size: 34rpx;
  min-height: 90rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  transition: border-color 0.3s ease;
}

.page .card .form-input:focus {
  border-color: #1aad19;
  box-shadow: 0 0 0 4rpx rgba(26, 173, 25, 0.1);
}

/* 列表项样式优化 */
.record-item {
  position: relative;
  overflow: hidden;
}

.record-item::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.05);
  opacity: 0;
  transition: opacity 0.2s;
}

.record-item:active::after {
  opacity: 1;
}

/* 金额高亮 */
.amount-highlight {
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: bold;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
}

.empty-icon {
  font-size: 120rpx;
  color: #ddd;
  margin-bottom: 30rpx;
}

.empty-text {
  color: #999;
  font-size: 32rpx;
  margin-bottom: 20rpx;
}

.empty-subtext {
  color: #ccc;
  font-size: 28rpx;
}
