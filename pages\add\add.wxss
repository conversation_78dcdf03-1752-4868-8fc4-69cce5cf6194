/* pages/add/add.wxss */

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  padding-bottom: 10rpx;
  border-bottom: 2rpx solid #1aad19;
}

.picker-view {
  color: #333;
  display: flex;
  align-items: center;
  min-height: 80rpx;
}

.picker-view:after {
  content: '';
  width: 0;
  height: 0;
  border-left: 10rpx solid transparent;
  border-right: 10rpx solid transparent;
  border-top: 10rpx solid #999;
  margin-left: auto;
}

.button-group {
  margin-top: 40rpx;
  padding: 0 20rpx;
}

.btn-secondary {
  background-color: #f5f5f5;
  color: #666;
  border-radius: 10rpx;
  padding: 20rpx;
  text-align: center;
  margin: 20rpx 0;
  border: 1rpx solid #ddd;
}

.btn-secondary:active {
  background-color: #e5e5e5;
}

/* 表单样式优化 */
.form-group {
  position: relative;
}

.form-label {
  position: relative;
}

.form-label:after {
  content: '*';
  color: #e64340;
  margin-left: 5rpx;
  display: none;
}

/* 必填字段标记 */
.form-group:nth-child(1) .form-label:after,
.form-group:nth-child(2) .form-label:after {
  display: inline;
}

.form-input:focus {
  border-color: #1aad19;
  box-shadow: 0 0 0 2rpx rgba(26, 173, 25, 0.2);
}

/* 文本域样式 */
textarea.form-input {
  min-height: 120rpx;
  resize: none;
}

/* 金额输入框样式 */
input[name="amount"] {
  font-size: 32rpx;
  font-weight: bold;
  color: #e64340;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .section-title {
    font-size: 30rpx;
  }
  
  .form-label {
    font-size: 28rpx;
  }
  
  .form-input {
    font-size: 28rpx;
    padding: 15rpx;
  }
}

/* 动画效果 */
.card {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    transform: translateY(30rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.button-group button {
  transition: all 0.2s ease;
}

.button-group button:active {
  transform: scale(0.98);
}
