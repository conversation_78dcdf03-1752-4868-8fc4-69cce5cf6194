# 微信小程序测试指南

## 快速开始测试

### 1. 环境准备
1. 下载微信开发者工具：https://developers.weixin.qq.com/miniprogram/dev/devtools/download.html
2. 安装并打开工具
3. 使用微信扫码登录

### 2. 创建项目
1. 点击"+"创建项目
2. 填写信息：
   - 项目名称：送礼账单
   - 目录：选择空文件夹
   - AppID：选择"测试号"
   - 模板：不使用模板

### 3. 导入代码
将以下文件按照目录结构创建到项目中：

```
项目根目录/
├── app.js
├── app.json  
├── app.wxss
├── sitemap.json
├── pages/
│   ├── index/
│   │   ├── index.js
│   │   ├── index.wxml
│   │   ├── index.wxss
│   │   └── index.json
│   ├── add/
│   │   ├── add.js
│   │   ├── add.wxml
│   │   ├── add.wxss
│   │   └── add.json
│   └── detail/
│       ├── detail.js
│       ├── detail.wxml
│       ├── detail.wxss
│       └── detail.json
└── utils/
    ├── dataModel.js
    ├── storage.js
    └── common.js
```

### 4. 测试功能

#### 基础功能测试
1. **启动应用**：点击"编译"按钮
2. **查看首页**：应该看到"暂无送礼记录"的提示
3. **添加记录**：
   - 点击底部"添加账单"标签
   - 填写表单信息
   - 点击"保存账单"
4. **查看列表**：返回首页查看新添加的记录
5. **查看详情**：点击记录查看详细信息
6. **编辑记录**：在详情页点击"编辑"
7. **删除记录**：在详情页点击"删除"

#### 高级功能测试
1. **搜索功能**：在首页搜索框输入关键词
2. **统计功能**：查看总记录数和总金额
3. **数据持久化**：关闭重开应用，数据应该保持

### 5. 调试技巧

#### 使用调试工具
1. **控制台**：查看console.log输出和错误信息
2. **网络**：查看请求（本项目主要是本地存储）
3. **存储**：查看Storage中的数据
4. **模拟器**：测试不同设备尺寸

#### 常见问题排查
1. **页面不显示**：检查路径配置和文件名
2. **数据不保存**：检查存储权限和代码逻辑
3. **样式问题**：检查wxss文件和选择器
4. **功能异常**：查看控制台错误信息

### 6. 真机测试

#### 预览功能
1. 点击工具栏"预览"按钮
2. 用微信扫描二维码
3. 在手机上测试真实体验

#### 注意事项
- 确保手机微信版本支持小程序
- 测试时注意网络环境
- 检查不同机型的兼容性

### 7. 性能测试

#### 测试项目
1. **启动速度**：应用打开时间
2. **页面切换**：页面间跳转流畅度
3. **数据加载**：大量数据时的性能
4. **内存使用**：长时间使用的稳定性

#### 优化建议
- 监控内存使用情况
- 优化图片资源大小
- 减少不必要的数据请求
- 使用合适的数据结构

### 8. 发布准备

#### 代码检查
1. 移除调试代码和console.log
2. 检查所有功能正常工作
3. 测试异常情况处理
4. 验证用户体验流程

#### 提交审核
1. 注册小程序账号
2. 配置小程序信息
3. 上传代码包
4. 提交审核

## 测试用例示例

### 添加记录测试
```
测试步骤：
1. 进入添加页面
2. 填写主人家名字：张三
3. 填写送礼人名字：李四
4. 填写地址：北京市朝阳区
5. 填写金额：500
6. 选择场合：结婚
7. 选择日期：今天
8. 点击保存

期望结果：
- 保存成功提示
- 返回列表页面
- 列表中显示新记录
- 统计数据更新
```

### 搜索功能测试
```
测试步骤：
1. 添加多条记录
2. 在搜索框输入"张三"
3. 查看搜索结果

期望结果：
- 只显示包含"张三"的记录
- 统计数据对应更新
- 清空搜索显示全部记录
```

## 常用快捷键

- `Ctrl/Cmd + S`：保存文件
- `Ctrl/Cmd + Shift + C`：打开调试器
- `Ctrl/Cmd + R`：刷新模拟器
- `F12`：打开开发者工具

## 技术支持

如遇到问题，可以：
1. 查看微信开发者文档
2. 在微信开发者社区提问
3. 检查代码和配置文件
4. 查看控制台错误信息
