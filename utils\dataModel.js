// 送礼账单数据模型
// utils/dataModel.js

/**
 * 送礼记录数据结构
 * @typedef {Object} GiftRecord
 * @property {number} id - 记录唯一标识
 * @property {string} hostName - 主人家名字
 * @property {string} giftGiverName - 送礼人名字
 * @property {string} giftGiverAddress - 送礼人地址
 * @property {number} amount - 送礼金额（单位：元）
 * @property {string} occasion - 送礼场合（如：结婚、生日、满月等）
 * @property {string} giftType - 礼品类型（如：现金、礼品等）
 * @property {string} note - 备注信息
 * @property {string} createTime - 创建时间（ISO字符串）
 * @property {string} updateTime - 更新时间（ISO字符串）
 * @property {string} eventDate - 事件日期（YYYY-MM-DD格式）
 */

/**
 * 创建新的送礼记录
 * @param {Object} data - 记录数据
 * @returns {GiftRecord} 完整的送礼记录对象
 */
function createGiftRecord(data) {
  const now = new Date().toISOString()
  return {
    id: Date.now(), // 简单的ID生成策略
    hostName: data.hostName || '',
    giftGiverName: data.giftGiverName || '',
    giftGiverAddress: data.giftGiverAddress || '',
    amount: parseFloat(data.amount) || 0,
    occasion: data.occasion || '',
    giftType: data.giftType || '现金',
    note: data.note || '',
    eventDate: data.eventDate || new Date().toISOString().split('T')[0],
    createTime: now,
    updateTime: now
  }
}

/**
 * 验证送礼记录数据
 * @param {Object} data - 要验证的数据
 * @returns {Object} 验证结果 {isValid: boolean, errors: string[]}
 */
function validateGiftRecord(data) {
  const errors = []
  
  if (!data.hostName || data.hostName.trim() === '') {
    errors.push('主人家名字不能为空')
  }
  
  if (!data.giftGiverName || data.giftGiverName.trim() === '') {
    errors.push('送礼人名字不能为空')
  }
  
  if (!data.amount || isNaN(parseFloat(data.amount)) || parseFloat(data.amount) <= 0) {
    errors.push('送礼金额必须是大于0的数字')
  }
  
  if (!data.eventDate) {
    errors.push('事件日期不能为空')
  }
  
  return {
    isValid: errors.length === 0,
    errors: errors
  }
}

/**
 * 格式化金额显示
 * @param {number} amount - 金额
 * @returns {string} 格式化后的金额字符串
 */
function formatAmount(amount) {
  return `¥${parseFloat(amount).toFixed(2)}`
}

/**
 * 格式化日期显示
 * @param {string} dateString - 日期字符串
 * @returns {string} 格式化后的日期
 */
function formatDate(dateString) {
  const date = new Date(dateString)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
}

module.exports = {
  createGiftRecord,
  validateGiftRecord,
  formatAmount,
  formatDate
}
