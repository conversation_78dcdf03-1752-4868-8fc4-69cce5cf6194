{"description": "项目配置文件", "miniprogramRoot": "./", "compileType": "miniprogram", "setting": {"useCompilerPlugins": ["typescript"], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "coverView": false, "postcss": false, "minified": false, "enhance": true, "showShadowRootInWxmlPanel": false, "packNpmRelationList": [], "ignoreUploadUnusedFiles": true, "compileHotReLoad": false, "skylineRenderEnable": true, "es6": true, "compileWorklet": false, "uglifyFileName": false, "uploadWithSourceMap": true, "packNpmManually": false, "minifyWXSS": true, "minifyWXML": true, "localPlugins": false, "condition": false, "swc": false, "disableSWC": true, "disableUseStrict": false}, "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "condition": {}, "srcMiniprogramRoot": "./", "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "libVersion": "3.9.0", "packOptions": {"ignore": [], "include": []}, "appid": "wx1662d4b24b34eb0c"}