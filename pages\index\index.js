// pages/index/index.js
const app = getApp()
const { formatAmount, formatDate } = require('../../utils/dataModel')

Page({
  data: {
    records: [],
    filteredRecords: [],
    searchKeyword: '',
    totalRecords: 0,
    totalAmount: '0.00'
  },

  onLoad() {
    this.loadRecords()
  },

  onShow() {
    // 每次显示页面时重新加载数据，以防从其他页面返回时数据有更新
    this.loadRecords()
  },

  // 加载送礼记录
  loadRecords() {
    const records = app.getGiftRecords()
    this.setData({
      records: records,
      filteredRecords: records
    })
    this.calculateStatistics(records)
  },

  // 计算统计信息
  calculateStatistics(records) {
    const totalRecords = records.length
    const totalAmount = records.reduce((sum, record) => sum + parseFloat(record.amount), 0)
    
    this.setData({
      totalRecords: totalRecords,
      totalAmount: totalAmount.toFixed(2)
    })
  },

  // 搜索输入处理
  onSearchInput(e) {
    const keyword = e.detail.value.toLowerCase()
    this.setData({
      searchKeyword: keyword
    })
    this.filterRecords(keyword)
  },

  // 过滤记录
  filterRecords(keyword) {
    if (!keyword) {
      this.setData({
        filteredRecords: this.data.records
      })
      this.calculateStatistics(this.data.records)
      return
    }

    const filtered = this.data.records.filter(record => {
      return record.hostName.toLowerCase().includes(keyword) ||
             record.giftGiverName.toLowerCase().includes(keyword) ||
             (record.giftGiverAddress && record.giftGiverAddress.toLowerCase().includes(keyword)) ||
             (record.occasion && record.occasion.toLowerCase().includes(keyword))
    })

    this.setData({
      filteredRecords: filtered
    })
    this.calculateStatistics(filtered)
  },

  // 跳转到详情页
  goToDetail(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/detail/detail?id=${id}`
    })
  },

  // 跳转到添加页面
  goToAdd() {
    wx.navigateTo({
      url: '/pages/add/add'
    })
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadRecords()
    wx.stopPullDownRefresh()
  }
})
