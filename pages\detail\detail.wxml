<!--pages/detail/detail.wxml-->
<view class="page">
  <view wx:if="{{record}}">
    <!-- 主要信息卡片 -->
    <view class="card main-info">
      <view class="host-name">{{record.hostName}}</view>
      <view class="amount-display">¥{{record.amount}}</view>
      <view class="gift-type">{{record.giftType}}</view>
    </view>

    <!-- 送礼人信息 -->
    <view class="card">
      <view class="section-title">送礼人信息</view>
      <view class="info-row">
        <view class="info-label">姓名</view>
        <view class="info-value">{{record.giftGiverName}}</view>
      </view>
      <view class="info-row" wx:if="{{record.giftGiverAddress}}">
        <view class="info-label">地址</view>
        <view class="info-value">{{record.giftGiverAddress}}</view>
      </view>
    </view>

    <!-- 事件信息 -->
    <view class="card">
      <view class="section-title">事件信息</view>
      <view class="info-row">
        <view class="info-label">场合</view>
        <view class="info-value">{{record.occasion || '未填写'}}</view>
      </view>
      <view class="info-row">
        <view class="info-label">日期</view>
        <view class="info-value">{{record.eventDate}}</view>
      </view>
      <view class="info-row" wx:if="{{record.note}}">
        <view class="info-label">备注</view>
        <view class="info-value note-text">{{record.note}}</view>
      </view>
    </view>

    <!-- 记录信息 -->
    <view class="card">
      <view class="section-title">记录信息</view>
      <view class="info-row">
        <view class="info-label">创建时间</view>
        <view class="info-value">{{createTimeFormatted}}</view>
      </view>
      <view class="info-row" wx:if="{{record.updateTime && record.updateTime !== record.createTime}}">
        <view class="info-label">更新时间</view>
        <view class="info-value">{{updateTimeFormatted}}</view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="btn-primary" bindtap="editRecord">编辑</button>
      <button class="btn-danger" bindtap="deleteRecord">删除</button>
    </view>
  </view>

  <!-- 记录不存在 -->
  <view wx:else class="card text-center" style="padding: 100rpx 20rpx;">
    <view class="text-muted" style="font-size: 32rpx;">记录不存在</view>
    <view class="text-muted" style="font-size: 28rpx; margin-top: 20rpx;">该记录可能已被删除</view>
    <button class="btn-primary" style="margin-top: 40rpx;" bindtap="goBack">返回列表</button>
  </view>
</view>
