<!--pages/add/add.wxml-->
<view class="page">
  <form bindsubmit="onSubmit">
    <!-- 基本信息 -->
    <view class="card">
      <view class="section-title">基本信息</view>
      
      <view class="form-group">
        <label class="form-label">主人家名字 *</label>
        <input class="form-input" name="hostName" placeholder="请输入主人家名字" value="{{formData.hostName}}" bindinput="onInputChange" data-field="hostName" />
      </view>

      <view class="form-group">
        <label class="form-label">送礼人名字 *</label>
        <input class="form-input" name="giftGiverName" placeholder="请输入送礼人名字" value="{{formData.giftGiverName}}" bindinput="onInputChange" data-field="giftGiverName" />
      </view>

      <view class="form-group">
        <label class="form-label">送礼人地址</label>
        <input class="form-input" name="giftGiverAddress" placeholder="请输入送礼人地址" value="{{formData.giftGiverAddress}}" bindinput="onInputChange" data-field="giftGiverAddress" />
      </view>
    </view>

    <!-- 礼金信息 -->
    <view class="card">
      <view class="section-title">礼金信息</view>
      
      <view class="form-group">
        <label class="form-label">送礼金额 * (元)</label>
        <input class="form-input" name="amount" type="digit" placeholder="请输入金额" value="{{formData.amount}}" bindinput="onInputChange" data-field="amount" />
      </view>

      <view class="form-group">
        <label class="form-label">礼品类型</label>
        <picker bindchange="onGiftTypeChange" value="{{giftTypeIndex}}" range="{{giftTypes}}">
          <view class="form-input picker-view">
            {{giftTypes[giftTypeIndex]}}
          </view>
        </picker>
      </view>
    </view>

    <!-- 事件信息 -->
    <view class="card">
      <view class="section-title">事件信息</view>
      
      <view class="form-group">
        <label class="form-label">送礼场合</label>
        <picker bindchange="onOccasionChange" value="{{occasionIndex}}" range="{{occasions}}">
          <view class="form-input picker-view">
            {{occasions[occasionIndex]}}
          </view>
        </picker>
      </view>

      <view class="form-group">
        <label class="form-label">事件日期 *</label>
        <picker mode="date" value="{{formData.eventDate}}" bindchange="onDateChange">
          <view class="form-input picker-view">
            {{formData.eventDate || '请选择日期'}}
          </view>
        </picker>
      </view>

      <view class="form-group">
        <label class="form-label">备注</label>
        <textarea class="form-input" name="note" placeholder="请输入备注信息" value="{{formData.note}}" bindinput="onInputChange" data-field="note" maxlength="200" auto-height />
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="button-group">
      <button class="btn-primary" form-type="submit">保存账单</button>
      <button class="btn-secondary" bindtap="onReset">重置</button>
    </view>
  </form>
</view>
