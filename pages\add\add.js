// pages/add/add.js
const app = getApp()
const { createGiftRecord, validateGiftRecord } = require('../../utils/dataModel')

Page({
  data: {
    formData: {
      hostName: '',
      giftGiverName: '',
      giftGiverAddress: '',
      amount: '',
      giftType: '现金',
      occasion: '',
      eventDate: '',
      note: ''
    },
    giftTypes: ['现金', '礼品', '红包', '其他'],
    giftTypeIndex: 0,
    occasions: ['结婚', '生日', '满月', '升学', '乔迁', '开业', '其他'],
    occasionIndex: 6, // 默认选择"其他"
    isEditing: false,
    editId: null
  },

  onLoad(options) {
    // 设置默认日期为今天
    const today = new Date().toISOString().split('T')[0]
    this.setData({
      'formData.eventDate': today
    })

    // 如果是编辑模式
    if (options.id) {
      this.setData({
        isEditing: true,
        editId: parseInt(options.id)
      })
      this.loadRecord(parseInt(options.id))
    }
  },

  // 加载要编辑的记录
  loadRecord(id) {
    const records = app.getGiftRecords()
    const record = records.find(r => r.id === id)
    if (record) {
      // 设置表单数据
      this.setData({
        formData: {
          hostName: record.hostName,
          giftGiverName: record.giftGiverName,
          giftGiverAddress: record.giftGiverAddress,
          amount: record.amount.toString(),
          giftType: record.giftType,
          occasion: record.occasion,
          eventDate: record.eventDate,
          note: record.note
        }
      })

      // 设置选择器索引
      const giftTypeIndex = this.data.giftTypes.indexOf(record.giftType)
      const occasionIndex = this.data.occasions.indexOf(record.occasion)
      
      this.setData({
        giftTypeIndex: giftTypeIndex >= 0 ? giftTypeIndex : 0,
        occasionIndex: occasionIndex >= 0 ? occasionIndex : 6
      })
    }
  },

  // 输入框变化处理
  onInputChange(e) {
    const field = e.currentTarget.dataset.field
    const value = e.detail.value
    this.setData({
      [`formData.${field}`]: value
    })
  },

  // 礼品类型选择
  onGiftTypeChange(e) {
    const index = parseInt(e.detail.value)
    this.setData({
      giftTypeIndex: index,
      'formData.giftType': this.data.giftTypes[index]
    })
  },

  // 送礼场合选择
  onOccasionChange(e) {
    const index = parseInt(e.detail.value)
    this.setData({
      occasionIndex: index,
      'formData.occasion': this.data.occasions[index]
    })
  },

  // 日期选择
  onDateChange(e) {
    this.setData({
      'formData.eventDate': e.detail.value
    })
  },

  // 表单提交
  onSubmit(e) {
    const formData = this.data.formData
    
    // 验证表单数据
    const validation = validateGiftRecord(formData)
    if (!validation.isValid) {
      wx.showToast({
        title: validation.errors[0],
        icon: 'none',
        duration: 2000
      })
      return
    }

    // 显示加载提示
    wx.showLoading({
      title: this.data.isEditing ? '更新中...' : '保存中...'
    })

    try {
      let success = false
      
      if (this.data.isEditing) {
        // 更新记录
        success = app.updateGiftRecord(this.data.editId, formData)
      } else {
        // 添加新记录
        success = app.addGiftRecord(formData)
      }

      wx.hideLoading()

      if (success) {
        wx.showToast({
          title: this.data.isEditing ? '更新成功' : '保存成功',
          icon: 'success',
          duration: 1500
        })

        // 延迟返回上一页
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      } else {
        wx.showToast({
          title: '保存失败，请重试',
          icon: 'none',
          duration: 2000
        })
      }
    } catch (error) {
      wx.hideLoading()
      wx.showToast({
        title: '操作失败',
        icon: 'none',
        duration: 2000
      })
      console.error('保存记录失败:', error)
    }
  },

  // 重置表单
  onReset() {
    wx.showModal({
      title: '确认重置',
      content: '确定要清空所有输入的内容吗？',
      success: (res) => {
        if (res.confirm) {
          const today = new Date().toISOString().split('T')[0]
          this.setData({
            formData: {
              hostName: '',
              giftGiverName: '',
              giftGiverAddress: '',
              amount: '',
              giftType: '现金',
              occasion: '',
              eventDate: today,
              note: ''
            },
            giftTypeIndex: 0,
            occasionIndex: 6
          })
        }
      }
    })
  }
})
