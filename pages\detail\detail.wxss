/* pages/detail/detail.wxss */

.main-info {
  text-align: center;
  background: linear-gradient(135deg, #1aad19, #4fc3f7);
  color: white;
  padding: 40rpx 20rpx;
}

.host-name {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.amount-display {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.gift-type {
  font-size: 28rpx;
  opacity: 0.9;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  padding-bottom: 10rpx;
  border-bottom: 2rpx solid #1aad19;
}

.info-row {
  display: flex;
  margin-bottom: 20rpx;
  align-items: flex-start;
}

.info-label {
  width: 160rpx;
  color: #666;
  font-size: 28rpx;
  flex-shrink: 0;
}

.info-value {
  flex: 1;
  color: #333;
  font-size: 28rpx;
  word-break: break-all;
}

.note-text {
  background-color: #f5f5f5;
  padding: 20rpx;
  border-radius: 8rpx;
  border-left: 4rpx solid #1aad19;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
  margin-top: 40rpx;
  padding: 0 20rpx;
}

.action-buttons button {
  flex: 1;
}

/* 卡片动画 */
.card {
  animation: fadeInUp 0.4s ease-out;
}

.card:nth-child(1) { animation-delay: 0.1s; }
.card:nth-child(2) { animation-delay: 0.2s; }
.card:nth-child(3) { animation-delay: 0.3s; }
.card:nth-child(4) { animation-delay: 0.4s; }

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .host-name {
    font-size: 32rpx;
  }
  
  .amount-display {
    font-size: 42rpx;
  }
  
  .info-label {
    width: 140rpx;
    font-size: 26rpx;
  }
  
  .info-value {
    font-size: 26rpx;
  }
}

/* 按钮悬停效果 */
.action-buttons button:active {
  transform: scale(0.98);
  transition: transform 0.1s;
}

/* 主信息卡片阴影效果 */
.main-info {
  box-shadow: 0 8rpx 30rpx rgba(26, 173, 25, 0.3);
}

/* 信息行悬停效果 */
.info-row:hover {
  background-color: rgba(26, 173, 25, 0.05);
  border-radius: 8rpx;
  padding: 10rpx;
  margin: 10rpx -10rpx;
  transition: all 0.2s ease;
}
