<!--pages/index/index.wxml-->
<view class="page">
  <!-- 头部统计信息 -->
  <view class="card">
    <view class="flex-between">
      <view>
        <text class="text-muted">总记录数</text>
        <view class="amount">{{totalRecords}}</view>
      </view>
      <view class="text-right">
        <text class="text-muted">总金额</text>
        <view class="amount">¥{{totalAmount}}</view>
      </view>
    </view>
  </view>

  <!-- 搜索框 -->
  <view class="card">
    <view class="form-group">
      <input class="form-input" placeholder="搜索主人家或送礼人姓名" bindinput="onSearchInput" value="{{searchKeyword}}" />
    </view>
  </view>

  <!-- 账单列表 -->
  <view wx:if="{{filteredRecords.length > 0}}">
    <view wx:for="{{filteredRecords}}" wx:key="id" class="card" bindtap="goToDetail" data-id="{{item.id}}">
      <view class="flex-between">
        <view>
          <view style="font-weight: bold; margin-bottom: 10rpx;">{{item.hostName}}</view>
          <view class="text-muted" style="font-size: 28rpx;">送礼人：{{item.giftGiverName}}</view>
          <view class="text-muted" style="font-size: 28rpx;">场合：{{item.occasion || '未填写'}}</view>
          <view class="text-muted" style="font-size: 24rpx;">{{item.eventDate}}</view>
        </view>
        <view class="text-right">
          <view class="amount">¥{{item.amount}}</view>
          <view class="text-muted" style="font-size: 24rpx;">{{item.giftType}}</view>
        </view>
      </view>
      
      <!-- 地址信息 -->
      <view wx:if="{{item.giftGiverAddress}}" class="text-muted" style="font-size: 24rpx; margin-top: 10rpx; border-top: 1rpx solid #eee; padding-top: 10rpx;">
        地址：{{item.giftGiverAddress}}
      </view>
      
      <!-- 备注信息 -->
      <view wx:if="{{item.note}}" class="text-muted" style="font-size: 24rpx; margin-top: 5rpx;">
        备注：{{item.note}}
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view wx:else class="card text-center" style="padding: 100rpx 20rpx;">
    <view class="text-muted" style="font-size: 32rpx;">暂无送礼记录</view>
    <view class="text-muted" style="font-size: 28rpx; margin-top: 20rpx;">点击下方"添加账单"开始记录</view>
  </view>

  <!-- 浮动添加按钮 -->
  <view class="floating-btn" bindtap="goToAdd">
    <text>+</text>
  </view>
</view>
