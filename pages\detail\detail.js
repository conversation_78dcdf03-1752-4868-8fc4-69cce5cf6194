// pages/detail/detail.js
const app = getApp()

Page({
  data: {
    record: null,
    recordId: null,
    createTimeFormatted: '',
    updateTimeFormatted: ''
  },

  onLoad(options) {
    if (options.id) {
      this.setData({
        recordId: parseInt(options.id)
      })
      this.loadRecord()
    }
  },

  onShow() {
    // 从编辑页面返回时重新加载数据
    if (this.data.recordId) {
      this.loadRecord()
    }
  },

  // 加载记录详情
  loadRecord() {
    const records = app.getGiftRecords()
    const record = records.find(r => r.id === this.data.recordId)
    
    if (record) {
      this.setData({
        record: record,
        createTimeFormatted: this.formatDateTime(record.createTime),
        updateTimeFormatted: this.formatDateTime(record.updateTime)
      })
    } else {
      this.setData({
        record: null
      })
    }
  },

  // 格式化日期时间
  formatDateTime(dateTimeString) {
    if (!dateTimeString) return ''
    
    const date = new Date(dateTimeString)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    
    return `${year}-${month}-${day} ${hours}:${minutes}`
  },

  // 编辑记录
  editRecord() {
    wx.navigateTo({
      url: `/pages/add/add?id=${this.data.recordId}`
    })
  },

  // 删除记录
  deleteRecord() {
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这条送礼记录吗？删除后无法恢复。',
      confirmText: '删除',
      confirmColor: '#e64340',
      success: (res) => {
        if (res.confirm) {
          this.performDelete()
        }
      }
    })
  },

  // 执行删除操作
  performDelete() {
    wx.showLoading({
      title: '删除中...'
    })

    try {
      const success = app.deleteGiftRecord(this.data.recordId)
      wx.hideLoading()

      if (success) {
        wx.showToast({
          title: '删除成功',
          icon: 'success',
          duration: 1500
        })

        // 延迟返回上一页
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      } else {
        wx.showToast({
          title: '删除失败，请重试',
          icon: 'none',
          duration: 2000
        })
      }
    } catch (error) {
      wx.hideLoading()
      wx.showToast({
        title: '删除失败',
        icon: 'none',
        duration: 2000
      })
      console.error('删除记录失败:', error)
    }
  },

  // 返回列表
  goBack() {
    wx.navigateBack()
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: `${this.data.record.giftGiverName}的送礼记录`,
      path: `/pages/detail/detail?id=${this.data.recordId}`
    }
  }
})
