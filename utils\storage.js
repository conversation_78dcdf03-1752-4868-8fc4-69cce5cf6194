// 本地存储工具类
// utils/storage.js

const STORAGE_KEYS = {
  GIFT_RECORDS: 'giftRecords',
  USER_SETTINGS: 'userSettings',
  APP_VERSION: 'appVersion'
}

/**
 * 存储管理器
 */
class StorageManager {
  
  /**
   * 保存数据到本地存储
   * @param {string} key 存储键
   * @param {any} data 要存储的数据
   * @returns {boolean} 是否保存成功
   */
  static setItem(key, data) {
    try {
      wx.setStorageSync(key, data)
      return true
    } catch (error) {
      console.error(`保存数据失败 [${key}]:`, error)
      return false
    }
  }

  /**
   * 从本地存储获取数据
   * @param {string} key 存储键
   * @param {any} defaultValue 默认值
   * @returns {any} 存储的数据或默认值
   */
  static getItem(key, defaultValue = null) {
    try {
      const data = wx.getStorageSync(key)
      return data !== '' ? data : defaultValue
    } catch (error) {
      console.error(`获取数据失败 [${key}]:`, error)
      return defaultValue
    }
  }

  /**
   * 删除本地存储中的数据
   * @param {string} key 存储键
   * @returns {boolean} 是否删除成功
   */
  static removeItem(key) {
    try {
      wx.removeStorageSync(key)
      return true
    } catch (error) {
      console.error(`删除数据失败 [${key}]:`, error)
      return false
    }
  }

  /**
   * 清空所有本地存储
   * @returns {boolean} 是否清空成功
   */
  static clear() {
    try {
      wx.clearStorageSync()
      return true
    } catch (error) {
      console.error('清空存储失败:', error)
      return false
    }
  }

  /**
   * 获取存储信息
   * @returns {Object} 存储信息
   */
  static getStorageInfo() {
    try {
      return wx.getStorageInfoSync()
    } catch (error) {
      console.error('获取存储信息失败:', error)
      return {
        keys: [],
        currentSize: 0,
        limitSize: 0
      }
    }
  }

  /**
   * 检查存储空间是否充足
   * @returns {boolean} 是否有足够空间
   */
  static hasEnoughSpace() {
    const info = this.getStorageInfo()
    const usageRatio = info.currentSize / info.limitSize
    return usageRatio < 0.9 // 使用率低于90%认为空间充足
  }
}

/**
 * 送礼记录存储管理
 */
class GiftRecordStorage {
  
  /**
   * 获取所有送礼记录
   * @returns {Array} 送礼记录数组
   */
  static getRecords() {
    return StorageManager.getItem(STORAGE_KEYS.GIFT_RECORDS, [])
  }

  /**
   * 保存送礼记录
   * @param {Array} records 送礼记录数组
   * @returns {boolean} 是否保存成功
   */
  static saveRecords(records) {
    if (!StorageManager.hasEnoughSpace()) {
      console.warn('存储空间不足')
      return false
    }
    return StorageManager.setItem(STORAGE_KEYS.GIFT_RECORDS, records)
  }

  /**
   * 添加单条记录
   * @param {Object} record 送礼记录
   * @returns {boolean} 是否添加成功
   */
  static addRecord(record) {
    const records = this.getRecords()
    record.id = Date.now()
    record.createTime = new Date().toISOString()
    record.updateTime = record.createTime
    records.unshift(record)
    return this.saveRecords(records)
  }

  /**
   * 更新记录
   * @param {number} id 记录ID
   * @param {Object} updatedData 更新的数据
   * @returns {boolean} 是否更新成功
   */
  static updateRecord(id, updatedData) {
    const records = this.getRecords()
    const index = records.findIndex(record => record.id === id)
    if (index !== -1) {
      records[index] = {
        ...records[index],
        ...updatedData,
        updateTime: new Date().toISOString()
      }
      return this.saveRecords(records)
    }
    return false
  }

  /**
   * 删除记录
   * @param {number} id 记录ID
   * @returns {boolean} 是否删除成功
   */
  static deleteRecord(id) {
    const records = this.getRecords()
    const filteredRecords = records.filter(record => record.id !== id)
    return this.saveRecords(filteredRecords)
  }

  /**
   * 根据ID获取单条记录
   * @param {number} id 记录ID
   * @returns {Object|null} 送礼记录或null
   */
  static getRecordById(id) {
    const records = this.getRecords()
    return records.find(record => record.id === id) || null
  }

  /**
   * 备份数据到云端（预留接口）
   * @returns {Promise<boolean>} 是否备份成功
   */
  static async backupToCloud() {
    // 这里可以实现云端备份逻辑
    // 比如上传到微信云开发或其他云服务
    console.log('云端备份功能待实现')
    return false
  }

  /**
   * 从云端恢复数据（预留接口）
   * @returns {Promise<boolean>} 是否恢复成功
   */
  static async restoreFromCloud() {
    // 这里可以实现从云端恢复数据的逻辑
    console.log('云端恢复功能待实现')
    return false
  }
}

module.exports = {
  StorageManager,
  GiftRecordStorage,
  STORAGE_KEYS
}
