/**app.wxss**/
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 200rpx 0;
  box-sizing: border-box;
}

/* 通用样式 */
.page {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.card {
  background-color: white;
  border-radius: 10rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.btn-primary {
  background-color: #1aad19;
  color: white;
  border-radius: 10rpx;
  padding: 20rpx;
  text-align: center;
  margin: 20rpx 0;
}

.btn-danger {
  background-color: #e64340;
  color: white;
  border-radius: 10rpx;
  padding: 20rpx;
  text-align: center;
  margin: 20rpx 0;
}

.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  margin-bottom: 10rpx;
  font-weight: bold;
  color: #333;
}

.form-input {
  width: 100%;
  padding: 30rpx 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  box-sizing: border-box;
  background-color: white;
  font-size: 32rpx;
  min-height: 80rpx;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-muted {
  color: #999;
  font-size: 28rpx;
}

.amount {
  color: #e64340;
  font-weight: bold;
  font-size: 32rpx;
}

.flex {
  display: flex;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}
